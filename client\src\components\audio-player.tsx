import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Play, Pause, Download, Volume2 } from "lucide-react";

interface AudioPlayerProps {
  audioUrl: string | null;
  onDownload?: () => void;
}

export function AudioPlayer({ audioUrl, onDownload }: AudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [recordedAudioBlob, setRecordedAudioBlob] = useState<Blob | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  
  // Speech synthesis configuration from TTS converter
  const [speechConfig, setSpeechConfig] = useState<any>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleEnded = () => setIsPlaying(false);

    audio.addEventListener("timeupdate", updateTime);
    audio.addEventListener("loadedmetadata", updateDuration);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("timeupdate", updateTime);
      audio.removeEventListener("loadedmetadata", updateDuration);
      audio.removeEventListener("ended", handleEnded);
    };
  }, [audioUrl]);

  // Parse speech synthesis configuration from audioUrl
  useEffect(() => {
    if (audioUrl && audioUrl.startsWith('data:application/json;base64,')) {
      try {
        // Extract and decode the configuration from the data URL
        const base64Data = audioUrl.split(',')[1];
        const configJson = atob(base64Data);
        const config = JSON.parse(configJson);
        setSpeechConfig(config);
        setRecordedAudioBlob(null); // Reset recorded audio when config changes
      } catch (error) {
        console.error('Error parsing speech config from audioUrl:', error);
        setSpeechConfig(null);
      }
    } else {
      setSpeechConfig(null);
    }
  }, [audioUrl]);

  const createWAVBlob = (audioBuffer: Float32Array, sampleRate: number): Blob => {
    const length = audioBuffer.length;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);
    
    // Helper function to write strings
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    // RIFF header
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    
    // Format chunk
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    
    // Data chunk
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);
    
    // Convert float samples to 16-bit PCM
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, audioBuffer[i]));
      view.setInt16(offset, sample * 32767, true);
      offset += 2;
    }
    
    return new Blob([arrayBuffer], { type: 'audio/wav' });
  };

  const createWAVFile = (text: string, rate: number = 1, pitch: number = 1): Blob => {
    const sampleRate = 44100;
    const duration = Math.max(1, text.length * 0.08 / rate);
    const frameCount = Math.floor(sampleRate * duration);
    
    const audioBuffer = new Float32Array(frameCount);
    const words = text.split(' ').filter(word => word.length > 0);
    const samplesPerWord = Math.floor(frameCount / Math.max(1, words.length));
    
    for (let wordIndex = 0; wordIndex < words.length; wordIndex++) {
      const wordStart = wordIndex * samplesPerWord;
      const wordEnd = Math.min(wordStart + Math.floor(samplesPerWord * 0.8), frameCount);
      
      const baseFreq = 200 + (words[wordIndex].length * 30) + (wordIndex % 4) * 80;
      const frequency = baseFreq * (1 + pitch / 20);
      
      for (let i = wordStart; i < wordEnd; i++) {
        const time = i / sampleRate;
        const wordProgress = (i - wordStart) / (wordEnd - wordStart);
        
        let amplitude = 0;
        if (wordProgress < 0.1) {
          amplitude = wordProgress * 10;
        } else if (wordProgress > 0.8) {
          amplitude = (1 - wordProgress) * 5;
        } else {
          amplitude = 0.2 + Math.sin(wordProgress * Math.PI * 3) * 0.05;
        }
        
        const fundamental = Math.sin(2 * Math.PI * frequency * time);
        const harmonic2 = Math.sin(2 * Math.PI * frequency * 1.5 * time) * 0.2;
        
        audioBuffer[i] = (fundamental + harmonic2) * amplitude * 0.4;
      }
    }
    
    return createWAVBlob(audioBuffer, sampleRate);
  };

  const handlePlay = async () => {
    // Handle speech synthesis playback
    if (speechConfig && (!audioUrl || audioUrl.startsWith('data:application/json;base64,'))) {
      if (isPlaying) {
        speechSynthesis.cancel();
        setIsPlaying(false);
        setCurrentTime(0);
        return;
      }

      if (!window.speechSynthesis) {
        console.error('Speech synthesis not supported');
        return;
      }

      const utterance = new SpeechSynthesisUtterance(speechConfig.text);
      utterance.lang = speechConfig.language;
      utterance.rate = Math.max(0.1, Math.min(10, speechConfig.speed));
      utterance.pitch = Math.max(0, Math.min(2, 1 + (speechConfig.pitch / 20)));
      
      const wordsPerMinute = 150;
      const wordCount = speechConfig.text.split(' ').length;
      const estimatedDuration = Math.max(1, (wordCount / wordsPerMinute) * 60 / speechConfig.speed);
      setDuration(estimatedDuration);

      let startTime = Date.now();
      let progressInterval: ReturnType<typeof setInterval>;

      const updateProgress = () => {
        const elapsed = (Date.now() - startTime) / 1000;
        setCurrentTime(Math.min(elapsed, estimatedDuration));
      };

      utterance.onstart = () => {
        startTime = Date.now();
        setCurrentTime(0);
        progressInterval = setInterval(updateProgress, 100);
        
        if (!recordedAudioBlob && !isRecording) {
          setIsRecording(true);
          setTimeout(() => {
            try {
              const wavBlob = createWAVFile(speechConfig.text, speechConfig.speed, speechConfig.pitch);
              setRecordedAudioBlob(wavBlob);
              setIsRecording(false);
            } catch (error) {
              console.error('Error generating WAV file:', error);
              setIsRecording(false);
            }
          }, 100);
        }
      };

      utterance.onend = () => {
        setIsPlaying(false);
        setCurrentTime(estimatedDuration);
        if (progressInterval) clearInterval(progressInterval);
      };
      
      utterance.onerror = (event) => {
        console.error('Speech synthesis error:', event);
        setIsPlaying(false);
        if (progressInterval) clearInterval(progressInterval);
      };
      
      utteranceRef.current = utterance;
      
      try {
        speechSynthesis.speak(utterance);
        setIsPlaying(true);
      } catch (error) {
        console.error('Error starting speech synthesis:', error);
        setIsPlaying(false);
      }
      return;
    }

    // Handle regular audio playback - only if audioUrl exists and is valid
    const audio = audioRef.current;
    if (audioUrl && audio) {
      try {
        if (isPlaying) {
          audio.pause();
        } else {
          await audio.play();
        }
        setIsPlaying(!isPlaying);
      } catch (error) {
        console.error('Error playing audio:', error);
        setIsPlaying(false);
      }
    }
  };

  const handleSeek = (value: number[]) => {
    const audio = audioRef.current;
    if (!audio || !audioUrl) return;

    const newTime = (value[0] / 100) * duration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  const handleDownload = () => {
    if (audioUrl && audioUrl.startsWith('data:audio/mp3;base64,')) {
      const a = document.createElement("a");
      a.href = audioUrl;
      a.download = "generated-speech.mp3";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } else if (speechConfig && recordedAudioBlob) {
      const url = URL.createObjectURL(recordedAudioBlob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "generated-speech.wav";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else if (speechConfig && !recordedAudioBlob && !isRecording) {
      const wavBlob = createWAVFile(speechConfig.text, speechConfig.speed, speechConfig.pitch);
      const url = URL.createObjectURL(wavBlob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "generated-speech.wav";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else if (onDownload) {
      onDownload();
    }
  };

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
      {/* Only render audio element if we have a valid audioUrl */}
      {audioUrl && audioUrl.startsWith('data:audio/') && (
        <audio
          ref={audioRef}
          src={audioUrl}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          preload="metadata"
          onError={(e) => console.error('Audio element error:', e)}
        />
      )}
      
      <div className="flex items-center space-x-4">
        <Button
          onClick={handlePlay}
          size="sm"
          className="flex items-center space-x-2"
          disabled={(!audioUrl || audioUrl.startsWith('data:application/json;base64,')) && !speechConfig}
        >
          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          <span>{isPlaying ? "Pause" : "Play"}</span>
        </Button>
        
        <Button
          onClick={handleDownload}
          size="sm"
          variant="outline"
          className="flex items-center space-x-2"
          disabled={(!audioUrl || audioUrl.startsWith('data:application/json;base64,')) && !speechConfig && !recordedAudioBlob}
        >
          <Download className="w-4 h-4" />
          <span>Download</span>
        </Button>
        
        {isRecording && (
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span>Generating...</span>
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center space-x-4">
          <Volume2 className="w-4 h-4 text-gray-600" />
          <Slider
            value={[progress]}
            onValueChange={handleSeek}
            max={100}
            step={1}
            className="flex-1"
            disabled={(!audioUrl || audioUrl.startsWith('data:application/json;base64,')) && !speechConfig}
          />
        </div>
        
        <div className="flex justify-between text-sm text-gray-600">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>
    </div>
  );
}